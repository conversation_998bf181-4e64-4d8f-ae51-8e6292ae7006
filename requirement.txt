Title:
As an Organization admin, I need to be able to set permissions for next gen devices


Description

We will be working on integrating the next generation of devices which have a whole host of commands provided at the following link: shared/protobuf-schemas/monf-protobufs-messages/wrappers.proto line 46

Create DML script to seed PermissionGroup, Permission, and TemplateRolePermission entries for all device commands. This only applies to the municipality OrgTypeIdentifier. We will be using these later when we integrate these devices.



Description
We need to define fine-grained permissions around every oneof command in our device API. To do this, write an idempotent SQL DML script that:

Creates {{PermissionGroups}}

Logs (regular logs)

Audit Logs

Configuration

Statistics

Realtime Data

Firmware Update (DFU)

Test

Creates {{Permission}} under the groups

Logs (regular logs)

logs_view

logs_clear

Audit Logs

audit_logs_view

audit_logs_clear

audit_logs_reset

Configuration

configuration_read

configuration_write

etc…..

For each {{Permission}}, create an entry for each {{TemplateRole}} in the {{TemplateRolePermission}} table.

Logs (regular logs)

logs_view

Admin

De<PERSON>ultValue=True

Manager

DefaultValue=True

Technician

DefaultValue=True

Anonymous

DefaultValue=False

logs_clear

Admin

DefaultValue=True

Manager

DefaultValue=True

Technician

DefaultValue=True

Anonymous

DefaultValue=False

etc….
